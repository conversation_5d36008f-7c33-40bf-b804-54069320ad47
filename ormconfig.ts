import './src/boilerplate.polyfill';

import dotenv from 'dotenv';
import { DataSource, DataSourceOptions } from 'typeorm';
import type { SeederOptions } from 'typeorm-extension';

import { UserSubscriber } from './src/entity-subscribers/user-subscriber';
import { SnakeNamingStrategy } from './src/snake-naming.strategy';

dotenv.config();

const options: DataSourceOptions & SeederOptions = {
  type: 'postgres',
  host: process.env.DB_HOST,
  port: Number(process.env.DB_PORT),
  username: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_DATABASE,
  synchronize: <PERSON><PERSON><PERSON>(process.env.ENABLE_SYNCHRONIZE),
  namingStrategy: new SnakeNamingStrategy(),
  entities: ['src/modules/**/*.entity{.ts,.js}'],
  migrations: ['src/database/migrations/*{.ts,.js}'],
  subscribers: [UserSubscriber],
  seeds: ['src/database/seeds/*.seeder{.ts,.js}'],
};

export const dataSource = new DataSource(options);
