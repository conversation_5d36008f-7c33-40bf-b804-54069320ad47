import type { DataSource } from 'typeorm';
import type { Seeder, SeederFactoryManager } from 'typeorm-extension';

import { CategoryEntity } from '../../modules/category/category.entity.ts';

export class CategorySeeder implements Seeder {
  public async run(
    dataSource: DataSource,
    factoryManager: SeederFactoryManager,
  ): Promise<any> {
    const repository = dataSource.getRepository(CategoryEntity);

    await repository.save([
      {
        id: '550e8400-e29b-41d4-a716-446655440001',
        name: 'Work',
        description: 'Tasks related to work and professional activities',
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440002',
        name: 'Personal',
        description: 'Personal tasks and activities',
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440003',
        name: 'Shopping',
        description: 'Shopping lists and purchase-related tasks',
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440004',
        name: 'Health',
        description: 'Health and fitness related tasks',
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440005',
        name: 'Education',
        description: 'Learning and educational tasks',
      },
    ]);

    // ---------------------------------------------------

    // const categoryFactory = factoryManager.get(CategoryEntity);
    // save 1 factory generated entity, to the database
    // await categoryFactory.save();

    // save 5 factory generated entities, to the database
    // const categories = await categoryFactory.saveMany(10);
  }
}
